/*
	需求：实现一个拖拽指令，可在父元素区域任意拖拽元素。

	思路：
		1、浏览器内任意拖动，drag 为可用鼠标拖拽区域
		2、鼠标按下(onmousedown)时记录目标元素当前的 left 和 top 值。
		3、鼠标移动(onmousemove)时计算每次移动的横向距离和纵向距离的变化值，并改变元素的 left 和 top 值
		4、鼠标松开(onmouseup)时完成一次拖拽

	使用：在 Dom 上加上 v-drag 即可
	<div class="dialog-model" v-drag></div>
*/
import type { Directive } from 'vue'
interface ElType extends HTMLElement {
  parentNode: any
}

const drag: Directive = {
  mounted: function (el: ElType, drag: any) {
    let flag = false

    // 如果没有传递值，则使用元素本身作为拖拽区域
    const dragArea = drag.value ? el.querySelector(drag.value) : el

    // 检查 dragArea 是否存在
    if (!dragArea) {
      console.warn('拖拽区域未找到:', drag.value)
      return
    }

    dragArea.style.cursor = 'move'

    dragArea.onmousedown = function (e1) {
      flag = true // 设置拖拽标志为true

      let disX = e1.pageX - el.offsetLeft
      let disY = e1.pageY - el.offsetTop

      e1.preventDefault() // 阻止默认事件
      e1.stopPropagation()
      document.onmousemove = function (e) {
        if (e.target !== dragArea && !flag) return

        let x = e.pageX - disX
        let y = e.pageY - disY
        let maxX = window.innerWidth - el.offsetWidth
        let maxY = window.innerHeight - el.offsetHeight

        e.preventDefault() // 阻止默认事件
        e.stopPropagation()

        if (x < 0) {
          x = 0
        } else if (x > maxX) {
          x = maxX
        }

        if (y < 0) {
          y = 0
        } else if (y > maxY) {
          y = maxY
        }
        el.style.left = x + 'px'
        el.style.top = y + 'px'
      }
      document.onmouseup = function (e2) {
        flag = false // 设置拖拽标志为false
        e2.preventDefault() // 阻止默认事件
        e2.stopPropagation()
        document.onmousemove = document.onmouseup = null
      }
    }
  },
}
export default drag
