import { request } from '@/utils/request'

// 水利对象分类-根据编码获取树
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'HP' },
  })
}

// 工程设备-列表分页查询
export function getDevicePage(data) {
  return request({
    url: '/base/device/page',
    method: 'post', 
    data,
  })
}

// 工程设备-删除
export function deleteDevice(params) {
  return request({
    url: '/base/device/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程设备-新增
export function addDevice(data) {
  return request({
    url: '/base/device/add',
    method: 'post',
    data,
  })
}

// 工程设备-更新
export function updateDevice(data) {
  return request({
    url: '/base/device/update',
    method: 'post',
    data,
  })
}

// 工程设备-详情
export function getDevice(params) {
  return request({
    url: '/base/device/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程信息-分页查询
export function getProjectPage(data) {
  return request({
    url: '/base/project/authority/page',
    method: 'post',
    data,
  })
}

// 工程信息-获取展示信息
export function getDisplayCodes(params) {
  return request({
    url: '/base/project/getDisplayCodes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程信息-扩展属性详情
export function getExtendAttribute(params) {
  return request({
    url: '/base/project/extendAttribute/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程信息-更新扩展属性
export function updateExtendAttribute(data) {
  return request({
    url: '/base/project/extendAttribute/update',
    method: 'post',
    data,
  })
}

// 工程附件 列表查询
export function getProjectAttachList(params) {
  return request({
    url: '/base/projectAttach/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程新增
export function addProjectAttach(data) { 
  return request({
    url: '/base/projectAttach/add',
    method: 'post',
    data,
  })
}

// 工程删除
export function deleteProjectAttach(params) {
  return request({
    url: '/base/projectAttach/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程附件---划界确权列表查询
export function getDemarcation(params) {
  return request({
    url: '/prjstd/attach/getDemarcation',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//工程信息-分页查询
export function getBaseProject(data) {
  return request({
    url: '/base/project/page',
    method: 'post',
    data,
  })
}

//列表分页----水利对象
export function getRepairByOId(data) {
  return request({
    url: '/emergency/repair/pageDetails',
    method: 'post',
    data,
  })
}

// 详情
export function getMaintenanceById(params) {
  return request({
    url: '/prjstd/maintenance/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

//视频监控-根据水利对象查询视频点信息
export function getVideoById(data) {
  return request({
    url: '/base/camera/object/getCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}

// 获取站点列表
export function getSiteList(data) {
  return request({
    url: '/base/site/object/getSiteList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}